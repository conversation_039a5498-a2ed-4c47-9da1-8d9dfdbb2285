import customtkinter as ctk
import json
import os
import time
import threading
import webbrowser
from typing import Dict, List, Optional
import subprocess
import sys

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class CodeTrainingApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Code Training App - 60 Days Challenge")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)

        # Initialize data
        self.languages = ["Python", "JavaScript", "Java", "C++", "C#", "Go", "Rust", "TypeScript", "PHP", "Ruby", "Swift", "Kotlin"]
        self.selected_language = ctk.StringVar(value="Python")
        self.selected_day = ctk.IntVar(value=1)
        
        # Progress and challenge tracking
        self.completed_quests = self.load_progress()
        self.challenge_attempts = self.load_challenge_progress()
        self.code_submissions = self.load_code_submissions()
        self.daily_time_spent = self.load_daily_time()

        # Timer variables
        self.timer_running = False
        self.timer_seconds = 0
        self.timer_thread = None
        self.timer_duration = 3600  # Default 1 hour
        self.timer_presets = {
            "30 Minuten": 1800,
            "1 Stunde": 3600,
            "1.5 Stunden": 5400,
            "2 Stunden": 7200,
            "Benutzerdefiniert": 0
        }
        self.selected_timer_preset = ctk.StringVar(value="1 Stunde")

        # Initialize quest data
        self.quest_data = self.initialize_comprehensive_quest_data()

        self.setup_ui()
        
    def load_progress(self) -> Dict[str, List[int]]:
        """Load progress from JSON file"""
        try:
            if os.path.exists("progress.json"):
                with open("progress.json", "r") as f:
                    return json.load(f)
        except:
            pass
        return {lang: [] for lang in self.languages}
    
    def save_progress(self):
        """Save progress to JSON file"""
        try:
            with open("progress.json", "w") as f:
                json.dump(self.completed_quests, f, indent=2)
        except Exception as e:
            print(f"Error saving progress: {e}")
    
    def load_challenge_progress(self) -> Dict[str, Dict[int, int]]:
        """Load challenge attempt progress from JSON file"""
        try:
            if os.path.exists("challenge_progress.json"):
                with open("challenge_progress.json", "r") as f:
                    return json.load(f)
        except:
            pass
        return {lang: {} for lang in self.languages}
    
    def save_challenge_progress(self):
        """Save challenge attempt progress to JSON file"""
        try:
            with open("challenge_progress.json", "w") as f:
                json.dump(self.challenge_attempts, f, indent=2)
        except Exception as e:
            print(f"Error saving challenge progress: {e}")
    
    def load_daily_time(self) -> Dict[str, int]:
        """Load daily time spent from JSON file"""
        try:
            if os.path.exists("daily_time.json"):
                with open("daily_time.json", "r") as f:
                    return json.load(f)
        except:
            pass
        return {}
    
    def save_daily_time(self):
        """Save daily time spent to JSON file"""
        try:
            with open("daily_time.json", "w") as f:
                json.dump(self.daily_time_spent, f, indent=2)
        except Exception as e:
            print(f"Error saving daily time: {e}")
    
    def load_code_submissions(self) -> Dict[str, Dict[int, Dict]]:
        """Load code submissions from JSON file"""
        try:
            if os.path.exists("code_submissions.json"):
                with open("code_submissions.json", "r") as f:
                    return json.load(f)
        except:
            pass
        return {lang: {} for lang in self.languages}
    
    def save_code_submissions(self):
        """Save code submissions to JSON file"""
        try:
            with open("code_submissions.json", "w") as f:
                json.dump(self.code_submissions, f, indent=2)
        except Exception as e:
            print(f"Error saving code submissions: {e}")

    def initialize_comprehensive_quest_data(self) -> Dict[str, Dict[int, Dict]]:
        """Initialize comprehensive quest data for all languages and days"""
        quest_data = {}

        for language in self.languages:
            quest_data[language] = {}
            for day in range(1, 61):
                quest_data[language][day] = self.generate_detailed_quest(language, day)

        return quest_data

    def generate_detailed_quest(self, language: str, day: int) -> Dict:
        """Generate detailed quest content for a specific language and day"""
        # Get quest templates
        quest_templates = {
            "Python": self.get_python_quests(),
            "JavaScript": self.get_javascript_quests(),
            "Java": self.get_java_quests(),
        }
        
        # Get the appropriate quest for the day
        quests = quest_templates.get(language, self.get_python_quests())
        quest_index = (day - 1) % len(quests)
        base_quest = quests[quest_index].copy()

        # Determine day type and adjust content
        day_type = self.get_day_type(day)
        
        # Adjust difficulty based on day
        if day <= 20:
            difficulty = "Beginner"
            difficulty_emoji = "🟢"
        elif day <= 40:
            difficulty = "Intermediate"
            difficulty_emoji = "🟡"
        else:
            difficulty = "Advanced"
            difficulty_emoji = "🔴"

        base_quest["day"] = day
        base_quest["day_type"] = day_type
        base_quest["difficulty"] = f"{difficulty_emoji} {difficulty}"
        base_quest["title"] = f"Tag {day}: {base_quest['title']}"
        
        return base_quest
    
    def get_day_type(self, day: int) -> str:
        """Determine if day is learning, review, or rest day"""
        if day % 7 == 0:
            return "review"
        elif day % 14 == 0:
            return "rest"
        else:
            return "learning"

    def get_python_quests(self) -> List[Dict]:
        """Get Python-specific quest data with beginner-friendly structure"""
        return [
            {
                "title": "Python Grundlagen - Variablen & Datentypen",
                "topic": "python variables datatypes",
                "description": "Lerne die Grundlagen von Python: Variablen, Strings, Zahlen und Listen.",
                "learning_time": "45-60 Minuten",
                "what_you_learn": [
                    "Wie man Variablen erstellt und verwendet",
                    "Unterschiede zwischen Strings, Zahlen und Listen",
                    "Grundlegende Operationen mit Datentypen",
                    "Wie man Benutzereingaben verarbeitet"
                ],
                "objectives": [
                    "Erstelle Variablen verschiedener Datentypen",
                    "Arbeite mit Strings und String-Methoden",
                    "Verwende Listen und Tupel",
                    "Führe einfache Berechnungen durch"
                ],
                "code_example": '''# Variablen und Datentypen
name = "Max Mustermann"
alter = 25
hobbies = ["Programmieren", "Lesen", "Sport"]

print(f"Hallo {name}, du bist {alter} Jahre alt!")
print(f"Deine Hobbies: {', '.join(hobbies)}")''',
                "challenge": {
                    "title": "Persönlicher Daten-Manager",
                    "description": "Erstelle ein Programm, das persönliche Daten sammelt und formatiert ausgibt.",
                    "instructions": [
                        "1. Erstelle Variablen für Name, Alter und Stadt",
                        "2. Erstelle eine Liste mit 3 Hobbies",
                        "3. Berechne das Geburtsjahr (2024 - Alter)",
                        "4. Gib alle Informationen formatiert aus"
                    ],
                    "starter_code": '''# Dein Code hier
# Schritt 1: Erstelle Variablen
name = "Dein Name"
alter = 0  # Dein Alter
stadt = "Deine Stadt"

# Schritt 2: Erstelle eine Liste mit Hobbies
hobbies = []  # Füge 3 Hobbies hinzu

# Schritt 3: Berechne Geburtsjahr
geburtsjahr = 0  # Berechnung hier

# Schritt 4: Gib Informationen aus
print("Meine Informationen:")
# Deine print-Anweisungen hier''',
                    "solution": '''# Lösung: Persönlicher Daten-Manager
name = "Anna Schmidt"
alter = 25
stadt = "Berlin"

hobbies = ["Programmieren", "Lesen", "Wandern"]

geburtsjahr = 2024 - alter

print("Meine Informationen:")
print(f"Name: {name}")
print(f"Alter: {alter} Jahre")
print(f"Stadt: {stadt}")
print(f"Geburtsjahr: {geburtsjahr}")
print(f"Hobbies: {', '.join(hobbies)}")
print(f"Anzahl Hobbies: {len(hobbies)}")''',
                    "test_cases": [
                        {
                            "description": "Programm sollte Name, Alter, Stadt ausgeben",
                            "expected_output": "Name, Alter, Stadt und Hobbies werden angezeigt"
                        },
                        {
                            "description": "Geburtsjahr sollte korrekt berechnet werden",
                            "expected_output": "Geburtsjahr = 2024 - Alter"
                        }
                    ],
                    "hints": [
                        "Verwende f-Strings für formatierte Ausgabe: f'Text {variable}'",
                        "Listen werden mit eckigen Klammern erstellt: [item1, item2]",
                        "join() verbindet Listenelemente: ', '.join(liste)"
                    ]
                },
                "youtube_links": [
                    {"title": "Python Grundlagen (Deutsch)", "url": "https://www.youtube.com/watch?v=_uQrJ0TkZlc", "language": "de", "duration": "45 min"},
                    {"title": "Python Variables and Data Types", "url": "https://www.youtube.com/watch?v=khKv-8q7YmY", "language": "en", "duration": "30 min"}
                ]
            },
            {
                "title": "Kontrollstrukturen - If/Else & Schleifen",
                "topic": "python control structures loops",
                "description": "Lerne Entscheidungen zu treffen und Code zu wiederholen.",
                "learning_time": "50-60 Minuten",
                "what_you_learn": [
                    "Wie man Bedingungen mit if/else erstellt",
                    "Verschiedene Vergleichsoperatoren verwenden",
                    "For- und while-Schleifen schreiben",
                    "Logische Operatoren (and, or, not) nutzen"
                ],
                "objectives": [
                    "Verwende if/elif/else Anweisungen",
                    "Schreibe for- und while-Schleifen",
                    "Kombiniere Bedingungen mit and/or",
                    "Arbeite mit range() und enumerate()"
                ],
                "code_example": '''# Kontrollstrukturen
zahlen = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

for zahl in zahlen:
    if zahl % 2 == 0:
        print(f"{zahl} ist gerade")
    else:
        print(f"{zahl} ist ungerade")''',
                "challenge": {
                    "title": "Zahlen-Klassifizierer",
                    "description": "Erstelle ein Programm, das Zahlen analysiert und klassifiziert.",
                    "instructions": [
                        "1. Erstelle eine Liste mit 10 Zahlen",
                        "2. Durchlaufe die Liste mit einer for-Schleife",
                        "3. Klassifiziere jede Zahl (gerade/ungerade, positiv/negativ)",
                        "4. Zähle verschiedene Kategorien"
                    ],
                    "starter_code": '''# Dein Code hier
zahlen = [12, -5, 8, 0, -3, 15, 7, -10, 4, 9]

# Zähler für verschiedene Kategorien
gerade_count = 0
ungerade_count = 0
positive_count = 0
negative_count = 0

# Schleife durch alle Zahlen
for zahl in zahlen:
    # Deine Klassifizierung hier
    pass

# Ausgabe der Ergebnisse
print("Ergebnisse:")
# Deine print-Anweisungen hier''',
                    "solution": '''# Lösung: Zahlen-Klassifizierer
zahlen = [12, -5, 8, 0, -3, 15, 7, -10, 4, 9]

gerade_count = 0
ungerade_count = 0
positive_count = 0
negative_count = 0

for zahl in zahlen:
    # Gerade/Ungerade prüfen
    if zahl % 2 == 0:
        gerade_count += 1
        print(f"{zahl} ist gerade")
    else:
        ungerade_count += 1
        print(f"{zahl} ist ungerade")

    # Positiv/Negativ prüfen
    if zahl > 0:
        positive_count += 1
    elif zahl < 0:
        negative_count += 1

print("\\nErgebnisse:")
print(f"Gerade Zahlen: {gerade_count}")
print(f"Ungerade Zahlen: {ungerade_count}")
print(f"Positive Zahlen: {positive_count}")
print(f"Negative Zahlen: {negative_count}")''',
                    "test_cases": [
                        {
                            "description": "Alle Zahlen werden klassifiziert",
                            "expected_output": "Jede Zahl wird als gerade/ungerade ausgegeben"
                        },
                        {
                            "description": "Zähler sind korrekt",
                            "expected_output": "Anzahl gerade/ungerade und positive/negative Zahlen"
                        }
                    ],
                    "hints": [
                        "Modulo-Operator % gibt den Rest einer Division zurück",
                        "zahl % 2 == 0 bedeutet, die Zahl ist gerade",
                        "Verwende += um Zähler zu erhöhen"
                    ]
                },
                "youtube_links": [
                    {"title": "Python Schleifen und Bedingungen (Deutsch)", "url": "https://www.youtube.com/watch?v=9Os0o3wzS_I", "language": "de", "duration": "40 min"},
                    {"title": "Python Control Structures", "url": "https://www.youtube.com/watch?v=Zp5MuPOtsSY", "language": "en", "duration": "35 min"}
                ]
            }
        ]

    def get_javascript_quests(self) -> List[Dict]:
        """Get JavaScript-specific quest data"""
        return [
            {
                "title": "JavaScript Grundlagen - DOM Manipulation",
                "topic": "javascript dom manipulation",
                "description": "Lerne JavaScript Grundlagen und wie man HTML-Elemente manipuliert.",
                "learning_time": "50-60 Minuten",
                "what_you_learn": [
                    "JavaScript Variablen und Datentypen",
                    "DOM-Elemente auswählen und ändern",
                    "Event Listener hinzufügen",
                    "Dynamische Inhalte erstellen"
                ],
                "objectives": [
                    "Verstehe Variablen (let, const, var)",
                    "Arbeite mit dem DOM",
                    "Füge Event Listener hinzu",
                    "Ändere HTML-Inhalte dynamisch"
                ],
                "code_example": '''// DOM Manipulation
const button = document.getElementById('myButton');
const output = document.getElementById('output');

button.addEventListener('click', function() {
    const name = prompt('Wie heißt du?');
    output.innerHTML = `<h2>Hallo ${name}!</h2>`;
    output.style.color = 'blue';
});''',
                "challenge": {
                    "title": "Interaktive Begrüßungsseite",
                    "description": "Erstelle eine Webseite mit interaktiven Elementen.",
                    "instructions": [
                        "1. Erstelle HTML-Elemente für Button und Ausgabe",
                        "2. Füge Event Listener für Button-Klick hinzu",
                        "3. Sammle Benutzereingabe mit prompt()",
                        "4. Zeige personalisierte Begrüßung an"
                    ],
                    "starter_code": '''// HTML: <button id="greetBtn">Begrüßen</button><div id="output"></div>
// Dein JavaScript Code hier

const button = document.getElementById('greetBtn');
const output = document.getElementById('output');

// Event Listener hinzufügen
button.addEventListener('click', function() {
    // Dein Code hier
});''',
                    "solution": '''const button = document.getElementById('greetBtn');
const output = document.getElementById('output');

button.addEventListener('click', function() {
    const name = prompt('Wie heißt du?');
    const age = prompt('Wie alt bist du?');

    if (name && age) {
        output.innerHTML = `
            <h2>Hallo ${name}!</h2>
            <p>Du bist ${age} Jahre alt.</p>
            <p>Willkommen auf unserer Seite!</p>
        `;
        output.style.color = 'green';
        output.style.border = '2px solid green';
        output.style.padding = '10px';
    }
});''',
                    "test_cases": [
                        {
                            "description": "Button reagiert auf Klick",
                            "expected_output": "Prompt-Dialoge erscheinen"
                        },
                        {
                            "description": "Personalisierte Ausgabe",
                            "expected_output": "Name und Alter werden angezeigt"
                        }
                    ],
                    "hints": [
                        "getElementById() wählt Elemente nach ID aus",
                        "addEventListener() fügt Event-Handler hinzu",
                        "innerHTML ändert den HTML-Inhalt eines Elements"
                    ]
                },
                "youtube_links": [
                    {"title": "JavaScript Grundlagen (Deutsch)", "url": "https://www.youtube.com/watch?v=dtKciwk_si4", "language": "de", "duration": "55 min"},
                    {"title": "JavaScript Basics for Beginners", "url": "https://www.youtube.com/watch?v=hdI2bqOjy3c", "language": "en", "duration": "45 min"}
                ]
            }
        ]

    def get_java_quests(self) -> List[Dict]:
        """Get Java-specific quest data"""
        return [
            {
                "title": "Java Grundlagen - Klassen & Objekte",
                "topic": "java oop classes",
                "description": "Lerne objektorientierte Programmierung mit Java.",
                "learning_time": "55-60 Minuten",
                "what_you_learn": [
                    "Klassen und Objekte erstellen",
                    "Konstruktoren verwenden",
                    "Methoden definieren und aufrufen",
                    "Kapselung mit private/public"
                ],
                "objectives": [
                    "Erstelle Klassen und Objekte",
                    "Verwende Konstruktoren und Methoden",
                    "Verstehe Vererbung und Polymorphismus",
                    "Arbeite mit Packages und Imports"
                ],
                "code_example": '''// Java Klassen
public class Auto {
    private String marke;
    private int baujahr;

    public Auto(String marke, int baujahr) {
        this.marke = marke;
        this.baujahr = baujahr;
    }

    public void fahren() {
        System.out.println(marke + " fährt!");
    }

    public int getAlter() {
        return 2024 - baujahr;
    }
}''',
                "challenge": {
                    "title": "Einfache Fahrzeugklasse",
                    "description": "Erstelle eine Klasse für Fahrzeuge mit grundlegenden Eigenschaften.",
                    "instructions": [
                        "1. Erstelle eine Klasse 'Fahrzeug'",
                        "2. Füge Eigenschaften hinzu (marke, modell, baujahr)",
                        "3. Erstelle einen Konstruktor",
                        "4. Füge Methoden hinzu (info anzeigen, alter berechnen)"
                    ],
                    "starter_code": '''// Dein Java Code hier
public class Fahrzeug {
    // Eigenschaften hier definieren

    // Konstruktor hier erstellen

    // Methoden hier hinzufügen
}

// Hauptklasse zum Testen
public class Main {
    public static void main(String[] args) {
        // Fahrzeug-Objekt erstellen und testen
    }
}''',
                    "solution": '''public class Fahrzeug {
    private String marke;
    private String modell;
    private int baujahr;

    public Fahrzeug(String marke, String modell, int baujahr) {
        this.marke = marke;
        this.modell = modell;
        this.baujahr = baujahr;
    }

    public void zeigeInfo() {
        System.out.println("Fahrzeug: " + marke + " " + modell);
        System.out.println("Baujahr: " + baujahr);
        System.out.println("Alter: " + berechneAlter() + " Jahre");
    }

    public int berechneAlter() {
        return 2024 - baujahr;
    }
}

public class Main {
    public static void main(String[] args) {
        Fahrzeug auto = new Fahrzeug("BMW", "X5", 2020);
        auto.zeigeInfo();
    }
}''',
                    "test_cases": [
                        {
                            "description": "Klasse kompiliert ohne Fehler",
                            "expected_output": "Keine Compiler-Fehler"
                        },
                        {
                            "description": "Objekt wird korrekt erstellt",
                            "expected_output": "Fahrzeuginfo wird angezeigt"
                        }
                    ],
                    "hints": [
                        "private Variablen sind nur innerhalb der Klasse sichtbar",
                        "Konstruktor hat den gleichen Namen wie die Klasse",
                        "this. bezieht sich auf das aktuelle Objekt"
                    ]
                },
                "youtube_links": [
                    {"title": "Java OOP Tutorial (Deutsch)", "url": "https://www.youtube.com/watch?v=java-oop-de", "language": "de", "duration": "60 min"},
                    {"title": "Java Classes and Objects", "url": "https://www.youtube.com/watch?v=java-classes", "language": "en", "duration": "45 min"}
                ]
            }
        ]

    def setup_ui(self):
        """Set up the main user interface based on the provided layout"""
        # Configure grid weights
        self.root.grid_columnconfigure(1, weight=1)
        self.root.grid_rowconfigure(1, weight=1)

        # Top header with timer
        self.setup_header()

        # Left sidebar with day navigation
        self.setup_sidebar()

        # Main content area
        self.setup_main_content()

        # Bottom bar with settings and links
        self.setup_bottom_bar()

        # Start timer update
        self.update_timer_display()

    def setup_header(self):
        """Set up the top header with timer matching the image layout"""
        header_frame = ctk.CTkFrame(self.root, height=80)
        header_frame.grid(row=0, column=0, columnspan=3, sticky="ew", padx=10, pady=(10, 5))
        header_frame.grid_propagate(False)

        # Left side - Timer display and controls
        left_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        left_frame.pack(side="left", padx=20, pady=10)

        # Timer display (matches image: "Timer 06:25 / 60min (11%)")
        self.timer_label = ctk.CTkLabel(
            left_frame,
            text="Timer 00:00 / 60min (0%)",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="white"
        )
        self.timer_label.pack(side="left", padx=(0, 10))

        # Timer control buttons
        timer_controls = ctk.CTkFrame(left_frame, fg_color="transparent")
        timer_controls.pack(side="left")

        self.start_timer_btn = ctk.CTkButton(
            timer_controls,
            text="▶",
            width=30,
            height=30,
            command=self.toggle_timer,
            font=ctk.CTkFont(size=12)
        )
        self.start_timer_btn.pack(side="left", padx=2)

        self.reset_timer_btn = ctk.CTkButton(
            timer_controls,
            text="⏹",
            width=30,
            height=30,
            command=self.reset_timer,
            font=ctk.CTkFont(size=12)
        )
        self.reset_timer_btn.pack(side="left", padx=2)

        # Center - Timer duration selector
        center_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        center_frame.pack(side="left", padx=20, pady=10)

        ctk.CTkLabel(
            center_frame,
            text="Timer Dauer:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color="white"
        ).pack()

        self.timer_preset_menu = ctk.CTkOptionMenu(
            center_frame,
            variable=self.selected_timer_preset,
            values=list(self.timer_presets.keys()),
            command=self.on_timer_preset_change,
            width=120,
            height=28
        )
        self.timer_preset_menu.pack(pady=5)

        # Right side - Clock and daily progress (matches image: "Uhr 23:39" and "Heute: 6:50 min")
        right_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        right_frame.pack(side="right", padx=20, pady=10)

        # Clock display
        self.clock_label = ctk.CTkLabel(
            right_frame,
            text="Uhr 00:00",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="white"
        )
        self.clock_label.pack()

        # Daily time progress
        self.daily_progress_label = ctk.CTkLabel(
            right_frame,
            text="Heute: 0:00 min",
            font=ctk.CTkFont(size=12),
            text_color="white"
        )
        self.daily_progress_label.pack(pady=2)

    def setup_sidebar(self):
        """Set up the left sidebar with day navigation and progressive unlocking"""
        sidebar_frame = ctk.CTkFrame(self.root, width=200)
        sidebar_frame.grid(row=1, column=0, sticky="nsew", padx=(10, 5), pady=5)
        sidebar_frame.grid_propagate(False)

        # Language selection at top of sidebar (matches image)
        lang_label = ctk.CTkLabel(
            sidebar_frame,
            text="Sprache:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        lang_label.pack(pady=(15, 5))

        self.language_dropdown = ctk.CTkOptionMenu(
            sidebar_frame,
            variable=self.selected_language,
            values=self.languages,
            command=self.on_language_change
        )
        self.language_dropdown.pack(pady=(0, 15), padx=10, fill="x")

        # Day navigation section
        days_label = ctk.CTkLabel(
            sidebar_frame,
            text="Tage:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        days_label.pack(pady=(10, 5))

        # Scrollable frame for day buttons
        self.days_scroll_frame = ctk.CTkScrollableFrame(sidebar_frame, height=400)
        self.days_scroll_frame.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        # Create day buttons with progressive unlocking
        self.day_buttons = []
        for day in range(1, 61):
            self.create_day_button(day)

        # Highlight first day and update colors
        self.update_day_button_colors()

    def create_day_button(self, day: int):
        """Create a day button with proper styling and locking mechanism"""
        # Determine day type and accessibility
        day_type = self.get_day_type(day)
        is_accessible = self.is_day_accessible(day)

        # Set emoji and text based on day type and accessibility
        if not is_accessible:
            emoji = "🔒"
            type_text = "Gesperrt"
            button_color = ("#404040", "#303030")  # Gray for locked
        elif day_type == "rest":
            emoji = "😴"
            type_text = "Ruhetag"
            button_color = ("#8B4513", "#654321")  # Brown for rest
        elif day_type == "review":
            emoji = "📚"
            type_text = "Wiederholung"
            button_color = ("#9932CC", "#7B68EE")  # Purple for review
        else:
            # Learning day - color by difficulty
            if day <= 20:
                emoji = "🟢"
                button_color = ("#228B22", "#32CD32")  # Green for beginner
            elif day <= 40:
                emoji = "🟡"
                button_color = ("#FFD700", "#FFA500")  # Yellow for intermediate
            else:
                emoji = "🔴"
                button_color = ("#DC143C", "#FF6347")  # Red for advanced
            type_text = "Lernen"

        # Create button text
        btn_text = f"{emoji} Tag {day}"
        if day_type != "learning" or not is_accessible:
            btn_text += f" ({type_text})"

        btn = ctk.CTkButton(
            self.days_scroll_frame,
            text=btn_text,
            width=160,
            height=35,
            command=lambda d=day: self.select_day(d) if is_accessible else self.show_locked_day_message(d),
            fg_color=button_color,
            font=ctk.CTkFont(size=11)
        )
        btn.pack(pady=2, fill="x")
        self.day_buttons.append(btn)

    def is_day_accessible(self, day: int) -> bool:
        """Check if a day is accessible based on progression system"""
        if day == 1:
            return True  # First day is always accessible

        language = self.selected_language.get()
        completed_days = self.completed_quests.get(language, [])

        # Can access day if previous day is completed
        return (day - 1) in completed_days

    def show_locked_day_message(self, day: int):
        """Show message when trying to access a locked day"""
        message_window = ctk.CTkToplevel(self.root)
        message_window.title("Tag gesperrt")
        message_window.geometry("350x200")
        message_window.transient(self.root)

        ctk.CTkLabel(
            message_window,
            text=f"🔒 Tag {day} ist gesperrt!",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        ctk.CTkLabel(
            message_window,
            text=f"Du musst zuerst Tag {day-1} abschließen,\num Tag {day} freizuschalten.",
            font=ctk.CTkFont(size=14),
            wraplength=300
        ).pack(pady=10)

        ctk.CTkButton(
            message_window,
            text="OK",
            command=message_window.destroy
        ).pack(pady=20)

    def setup_main_content(self):
        """Set up the main content area with quest display and integrated code editor"""
        self.main_content_frame = ctk.CTkScrollableFrame(self.root)
        self.main_content_frame.grid(row=1, column=1, sticky="nsew", padx=5, pady=5)

        self.update_quest_display()

    def update_quest_display(self):
        """Update the quest display with current selection"""
        # Clear existing content
        for widget in self.main_content_frame.winfo_children():
            widget.destroy()

        language = self.selected_language.get()
        day = self.selected_day.get()

        # Check if day is accessible
        if not self.is_day_accessible(day):
            self.show_locked_content()
            return

        quest = self.quest_data[language][day]

        # Quest title (matches image layout)
        title_label = ctk.CTkLabel(
            self.main_content_frame,
            text=quest["title"],
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color="white"
        )
        title_label.pack(pady=(20, 15))

        # Difficulty badge (matches image: "Beginner" badge)
        difficulty_frame = ctk.CTkFrame(self.main_content_frame)
        difficulty_frame.pack(pady=10)

        difficulty_label = ctk.CTkLabel(
            difficulty_frame,
            text=quest["difficulty"],
            font=ctk.CTkFont(size=16, weight="bold")
        )
        difficulty_label.pack(padx=20, pady=10)

        # Description
        desc_label = ctk.CTkLabel(
            self.main_content_frame,
            text=quest["description"],
            font=ctk.CTkFont(size=16),
            wraplength=700,
            text_color="white"
        )
        desc_label.pack(pady=15, padx=20)

        # "What you will learn today" section
        if "what_you_learn" in quest:
            self.add_what_you_learn_section(quest["what_you_learn"])

        # Learning objectives section (matches image: "Lernziele:")
        self.add_objectives_section(quest["objectives"])

        # Code example section (matches image: "Code Beispiel:")
        if "code_example" in quest:
            self.add_code_example_section(quest["code_example"])

        # Daily challenge section with integrated editor
        if "challenge" in quest and quest["day_type"] == "learning":
            self.add_challenge_section(quest["challenge"])

        # YouTube links section
        if "youtube_links" in quest:
            self.add_youtube_section(quest["youtube_links"])

    def show_locked_content(self):
        """Show locked content message"""
        ctk.CTkLabel(
            self.main_content_frame,
            text="🔒 Dieser Tag ist gesperrt",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color="gray"
        ).pack(pady=50)

        ctk.CTkLabel(
            self.main_content_frame,
            text="Schließe den vorherigen Tag ab, um fortzufahren.",
            font=ctk.CTkFont(size=16),
            text_color="gray"
        ).pack(pady=10)

    def add_what_you_learn_section(self, what_you_learn):
        """Add 'What you will learn today' section"""
        learn_frame = ctk.CTkFrame(self.main_content_frame)
        learn_frame.pack(fill="x", pady=15, padx=20)

        learn_title = ctk.CTkLabel(
            learn_frame,
            text="📖 Was du heute lernst:",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        learn_title.pack(pady=(15, 10))

        for item in what_you_learn:
            learn_item = ctk.CTkLabel(
                learn_frame,
                text=f"• {item}",
                font=ctk.CTkFont(size=14),
                anchor="w"
            )
            learn_item.pack(anchor="w", padx=30, pady=3, fill="x")

    def add_objectives_section(self, objectives):
        """Add learning objectives section (matches image)"""
        obj_frame = ctk.CTkFrame(self.main_content_frame)
        obj_frame.pack(fill="x", pady=15, padx=20)

        obj_title = ctk.CTkLabel(
            obj_frame,
            text="🎯 Lernziele:",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        obj_title.pack(pady=(15, 10))

        for obj in objectives:
            obj_item = ctk.CTkLabel(
                obj_frame,
                text=f"• {obj}",
                font=ctk.CTkFont(size=14),
                anchor="w"
            )
            obj_item.pack(anchor="w", padx=30, pady=3, fill="x")

    def add_code_example_section(self, code_example):
        """Add code example section (matches image)"""
        code_frame = ctk.CTkFrame(self.main_content_frame)
        code_frame.pack(fill="x", pady=15, padx=20)

        code_title = ctk.CTkLabel(
            code_frame,
            text="💻 Code Beispiel:",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        code_title.pack(pady=(15, 10))

        code_text = ctk.CTkTextbox(
            code_frame,
            height=200,
            font=ctk.CTkFont(family="Courier", size=12)
        )
        code_text.pack(fill="x", padx=20, pady=(0, 15))
        code_text.insert("1.0", code_example)
        code_text.configure(state="disabled")

    def add_challenge_section(self, challenge):
        """Add daily challenge section with integrated code editor"""
        challenge_frame = ctk.CTkFrame(self.main_content_frame)
        challenge_frame.pack(fill="x", pady=15, padx=20)

        # Challenge title
        challenge_title = ctk.CTkLabel(
            challenge_frame,
            text="🎯 Tägliche Herausforderung:",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        challenge_title.pack(pady=(15, 10))

        # Challenge description and instructions
        challenge_desc = ctk.CTkLabel(
            challenge_frame,
            text=f"{challenge['title']}: {challenge['description']}",
            font=ctk.CTkFont(size=14, weight="bold"),
            wraplength=650
        )
        challenge_desc.pack(pady=(0, 10), padx=20)

        # Step-by-step instructions
        if "instructions" in challenge:
            instructions_frame = ctk.CTkFrame(challenge_frame)
            instructions_frame.pack(fill="x", padx=20, pady=10)

            ctk.CTkLabel(
                instructions_frame,
                text="📋 Schritt-für-Schritt Anleitung:",
                font=ctk.CTkFont(size=16, weight="bold")
            ).pack(pady=(10, 5))

            for instruction in challenge["instructions"]:
                ctk.CTkLabel(
                    instructions_frame,
                    text=instruction,
                    font=ctk.CTkFont(size=12),
                    anchor="w"
                ).pack(anchor="w", padx=20, pady=2, fill="x")

        # Attempt counter and progress
        language = self.selected_language.get()
        day = self.selected_day.get()
        attempts = self.challenge_attempts.get(language, {}).get(str(day), 0)

        progress_frame = ctk.CTkFrame(challenge_frame)
        progress_frame.pack(fill="x", padx=20, pady=10)

        attempt_label = ctk.CTkLabel(
            progress_frame,
            text=f"Versuche: {attempts}/5",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        attempt_label.pack(side="left", padx=20, pady=10)

        # Show hints button
        if "hints" in challenge:
            hints_btn = ctk.CTkButton(
                progress_frame,
                text="💡 Hinweise anzeigen",
                command=lambda: self.show_hints(challenge["hints"]),
                width=140
            )
            hints_btn.pack(side="right", padx=20, pady=10)

        # Integrated code editor
        editor_frame = ctk.CTkFrame(challenge_frame)
        editor_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(
            editor_frame,
            text="💻 Code Editor:",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))

        self.code_editor = ctk.CTkTextbox(
            editor_frame,
            height=300,
            font=ctk.CTkFont(family="Courier", size=12),
            wrap="none"
        )
        self.code_editor.pack(fill="x", padx=20, pady=10)

        # Load starter code or previous submission
        starter_code = self.get_starter_code_for_challenge(language, day, challenge)
        self.code_editor.insert("1.0", starter_code)

        # Challenge action buttons
        button_frame = ctk.CTkFrame(challenge_frame)
        button_frame.pack(pady=15)

        # Test code button
        test_btn = ctk.CTkButton(
            button_frame,
            text="🧪 Code testen",
            command=lambda: self.test_challenge_code(challenge),
            fg_color=("#2E8B57", "#228B22"),
            width=120
        )
        test_btn.pack(side="left", padx=5)

        # Submit solution button
        submit_btn = ctk.CTkButton(
            button_frame,
            text="📤 Lösung einreichen",
            command=lambda: self.submit_challenge_solution(challenge),
            fg_color=("#3B8ED0", "#1F6AA5"),
            width=140
        )
        submit_btn.pack(side="left", padx=5)

        # Show solution button (only if 5 attempts reached)
        if attempts >= 5:
            solution_btn = ctk.CTkButton(
                button_frame,
                text="💡 Lösung anzeigen",
                command=lambda: self.show_challenge_solution(challenge),
                fg_color=("#FF6B6B", "#CC5555"),
                width=130
            )
            solution_btn.pack(side="left", padx=5)

        # Test cases display
        if "test_cases" in challenge:
            self.add_test_cases_section(challenge_frame, challenge["test_cases"])

    def get_starter_code_for_challenge(self, language: str, day: int, challenge: Dict) -> str:
        """Get starter code for challenge, either from previous submission or default"""
        # Check if there's a previous submission
        if language in self.code_submissions and str(day) in self.code_submissions[language]:
            return self.code_submissions[language][str(day)].get("code", challenge.get("starter_code", ""))

        return challenge.get("starter_code", "# Dein Code hier...")

    def add_test_cases_section(self, parent, test_cases):
        """Add test cases section to show expected behavior"""
        test_frame = ctk.CTkFrame(parent)
        test_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(
            test_frame,
            text="✅ Erwartete Ergebnisse:",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))

        for i, test_case in enumerate(test_cases, 1):
            test_label = ctk.CTkLabel(
                test_frame,
                text=f"{i}. {test_case['description']}: {test_case['expected_output']}",
                font=ctk.CTkFont(size=12),
                anchor="w"
            )
            test_label.pack(anchor="w", padx=20, pady=2, fill="x")

    def show_hints(self, hints):
        """Show hints dialog for the challenge"""
        hints_window = ctk.CTkToplevel(self.root)
        hints_window.title("Hinweise")
        hints_window.geometry("500x400")
        hints_window.transient(self.root)

        ctk.CTkLabel(
            hints_window,
            text="💡 Hilfreiche Hinweise",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        scroll_frame = ctk.CTkScrollableFrame(hints_window)
        scroll_frame.pack(fill="both", expand=True, padx=20, pady=10)

        for i, hint in enumerate(hints, 1):
            hint_label = ctk.CTkLabel(
                scroll_frame,
                text=f"{i}. {hint}",
                font=ctk.CTkFont(size=12),
                wraplength=450,
                anchor="w"
            )
            hint_label.pack(anchor="w", pady=5, fill="x")

        ctk.CTkButton(
            hints_window,
            text="Schließen",
            command=hints_window.destroy
        ).pack(pady=10)

    def test_challenge_code(self, challenge):
        """Test the challenge code and provide feedback"""
        code = self.code_editor.get("1.0", "end-1c")

        if not code.strip():
            self.show_feedback("Fehler", "Bitte schreibe zuerst etwas Code!", "error")
            return

        # Simple code validation (can be enhanced)
        feedback_window = ctk.CTkToplevel(self.root)
        feedback_window.title("Code Test")
        feedback_window.geometry("400x300")
        feedback_window.transient(self.root)

        ctk.CTkLabel(
            feedback_window,
            text="🧪 Code Test Ergebnis",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        # Basic syntax check
        try:
            compile(code, '<string>', 'exec')
            result_text = "✅ Code kompiliert erfolgreich!\n\nDein Code scheint syntaktisch korrekt zu sein."
            color = "green"
        except SyntaxError as e:
            result_text = f"❌ Syntax Fehler gefunden:\n\n{str(e)}\n\nBitte überprüfe deinen Code."
            color = "red"

        result_label = ctk.CTkLabel(
            feedback_window,
            text=result_text,
            font=ctk.CTkFont(size=12),
            wraplength=350,
            text_color=color
        )
        result_label.pack(pady=20, padx=20)

        ctk.CTkButton(
            feedback_window,
            text="OK",
            command=feedback_window.destroy
        ).pack(pady=10)

    def submit_challenge_solution(self, challenge):
        """Submit challenge solution and handle progression"""
        code = self.code_editor.get("1.0", "end-1c")

        if not code.strip():
            self.show_feedback("Fehler", "Bitte schreibe zuerst etwas Code!", "error")
            return

        language = self.selected_language.get()
        day = self.selected_day.get()

        # Initialize if needed
        if language not in self.challenge_attempts:
            self.challenge_attempts[language] = {}
        if language not in self.code_submissions:
            self.code_submissions[language] = {}

        # Increment attempts
        current_attempts = self.challenge_attempts[language].get(str(day), 0)
        self.challenge_attempts[language][str(day)] = current_attempts + 1

        # Save submission
        self.code_submissions[language][str(day)] = {
            "code": code,
            "timestamp": time.time(),
            "attempt": current_attempts + 1
        }

        # Mark day as completed (simplified - in real app, you'd validate the solution)
        if language not in self.completed_quests:
            self.completed_quests[language] = []

        if day not in self.completed_quests[language]:
            self.completed_quests[language].append(day)

        # Save progress
        self.save_challenge_progress()
        self.save_code_submissions()
        self.save_progress()

        # Show success feedback and unlock next day
        self.show_submission_success(current_attempts + 1, day)

        # Refresh display
        self.update_day_button_colors()
        self.update_quest_display()

    def show_submission_success(self, attempt_number: int, day: int):
        """Show success message after code submission"""
        success_window = ctk.CTkToplevel(self.root)
        success_window.title("Lösung eingereicht!")
        success_window.geometry("400x250")
        success_window.transient(self.root)

        ctk.CTkLabel(
            success_window,
            text="🎉 Glückwunsch!",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color="green"
        ).pack(pady=20)

        message = f"Du hast Tag {day} erfolgreich abgeschlossen!\n\n"
        message += f"Versuch: {attempt_number}/5\n\n"

        if day < 60:
            message += f"Tag {day + 1} ist jetzt freigeschaltet!"
        else:
            message += "Du hast alle 60 Tage abgeschlossen! 🏆"

        ctk.CTkLabel(
            success_window,
            text=message,
            font=ctk.CTkFont(size=14),
            wraplength=350
        ).pack(pady=10)

        button_frame = ctk.CTkFrame(success_window, fg_color="transparent")
        button_frame.pack(pady=20)

        if day < 60:
            next_btn = ctk.CTkButton(
                button_frame,
                text=f"Zu Tag {day + 1}",
                command=lambda: [success_window.destroy(), self.select_day(day + 1)],
                fg_color=("green", "darkgreen")
            )
            next_btn.pack(side="left", padx=5)

        ctk.CTkButton(
            button_frame,
            text="OK",
            command=success_window.destroy
        ).pack(side="left", padx=5)

    def show_challenge_solution(self, challenge):
        """Show the example solution for the challenge"""
        solution_window = ctk.CTkToplevel(self.root)
        solution_window.title("Musterlösung")
        solution_window.geometry("700x600")
        solution_window.transient(self.root)

        ctk.CTkLabel(
            solution_window,
            text="💡 Musterlösung",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=10)

        ctk.CTkLabel(
            solution_window,
            text="Du hast 5 Versuche erreicht. Hier ist eine Beispiellösung:",
            font=ctk.CTkFont(size=12),
            text_color="orange"
        ).pack(pady=5)

        solution_text = ctk.CTkTextbox(
            solution_window,
            font=ctk.CTkFont(family="Courier", size=12)
        )
        solution_text.pack(fill="both", expand=True, padx=20, pady=20)
        solution_text.insert("1.0", challenge.get("solution", "Keine Lösung verfügbar"))
        solution_text.configure(state="disabled")

        button_frame = ctk.CTkFrame(solution_window, fg_color="transparent")
        button_frame.pack(pady=10)

        copy_btn = ctk.CTkButton(
            button_frame,
            text="In Editor kopieren",
            command=lambda: self.copy_solution_to_editor(challenge.get("solution", "")),
            width=140
        )
        copy_btn.pack(side="left", padx=5)

        ctk.CTkButton(
            button_frame,
            text="Schließen",
            command=solution_window.destroy
        ).pack(side="left", padx=5)

    def copy_solution_to_editor(self, solution: str):
        """Copy solution to the code editor"""
        if hasattr(self, 'code_editor'):
            self.code_editor.delete("1.0", "end")
            self.code_editor.insert("1.0", solution)

    def show_feedback(self, title: str, message: str, type: str = "info"):
        """Show feedback dialog"""
        feedback_window = ctk.CTkToplevel(self.root)
        feedback_window.title(title)
        feedback_window.geometry("350x200")
        feedback_window.transient(self.root)

        color = {"error": "red", "success": "green", "info": "blue"}.get(type, "blue")

        ctk.CTkLabel(
            feedback_window,
            text=message,
            font=ctk.CTkFont(size=14),
            wraplength=300,
            text_color=color
        ).pack(pady=30)

        ctk.CTkButton(
            feedback_window,
            text="OK",
            command=feedback_window.destroy
        ).pack(pady=10)

    def add_youtube_section(self, youtube_links):
        """Add YouTube links section"""
        youtube_frame = ctk.CTkFrame(self.main_content_frame)
        youtube_frame.pack(fill="x", pady=15, padx=20)

        youtube_title = ctk.CTkLabel(
            youtube_frame,
            text="📺 Lernvideos:",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        youtube_title.pack(pady=(15, 10))

        for link in youtube_links:
            link_frame = ctk.CTkFrame(youtube_frame)
            link_frame.pack(fill="x", padx=20, pady=5)

            flag = "🇩🇪" if link.get("language") == "de" else "🇺🇸"

            link_btn = ctk.CTkButton(
                link_frame,
                text=f"{flag} {link['title']} ({link.get('duration', 'N/A')})",
                command=lambda url=link['url']: webbrowser.open(url),
                anchor="w"
            )
            link_btn.pack(fill="x", padx=10, pady=5)

    def setup_bottom_bar(self):
        """Set up the bottom bar with settings and links (matches image)"""
        bottom_frame = ctk.CTkFrame(self.root, height=60)
        bottom_frame.grid(row=2, column=0, columnspan=3, sticky="ew", padx=10, pady=(5, 10))
        bottom_frame.grid_propagate(False)

        # Settings button (left)
        settings_btn = ctk.CTkButton(
            bottom_frame,
            text="Settings",
            width=100,
            command=self.open_settings
        )
        settings_btn.pack(side="left", padx=20, pady=15)

        # Right side buttons (matches image: "Bearbeiten" and "YouTube Links")
        right_frame = ctk.CTkFrame(bottom_frame, fg_color="transparent")
        right_frame.pack(side="right", padx=20, pady=10)

        # Edit button
        edit_btn = ctk.CTkButton(
            right_frame,
            text="Bearbeiten",
            width=100,
            command=self.open_editor
        )
        edit_btn.pack(side="left", padx=5)

        # YouTube links button
        youtube_btn = ctk.CTkButton(
            right_frame,
            text="📺 YouTube Links",
            width=140,
            command=self.open_youtube_links
        )
        youtube_btn.pack(side="left", padx=5)

    # Navigation and selection methods
    def select_day(self, day: int):
        """Select a specific day"""
        if not self.is_day_accessible(day):
            self.show_locked_day_message(day)
            return

        self.selected_day.set(day)
        self.update_day_button_colors()
        self.update_quest_display()

    def update_day_button_colors(self):
        """Update day button colors based on completion status and accessibility"""
        current_day = self.selected_day.get()
        language = self.selected_language.get()
        completed_days = self.completed_quests.get(language, [])

        for i, btn in enumerate(self.day_buttons):
            day = i + 1
            is_accessible = self.is_day_accessible(day)

            if day == current_day and is_accessible:
                btn.configure(fg_color=("#1F6AA5", "#144870"))  # Selected (blue)
            elif day in completed_days:
                btn.configure(fg_color=("#2CC985", "#2FA572"))  # Completed (green)
            elif not is_accessible:
                btn.configure(fg_color=("#404040", "#303030"))  # Locked (gray)
            else:
                # Default colors based on day type and difficulty
                day_type = self.get_day_type(day)
                if day_type == "rest":
                    btn.configure(fg_color=("#8B4513", "#654321"))  # Brown
                elif day_type == "review":
                    btn.configure(fg_color=("#9932CC", "#7B68EE"))  # Purple
                else:
                    if day <= 20:
                        btn.configure(fg_color=("#228B22", "#32CD32"))  # Green
                    elif day <= 40:
                        btn.configure(fg_color=("#FFD700", "#FFA500"))  # Yellow
                    else:
                        btn.configure(fg_color=("#DC143C", "#FF6347"))  # Red

    def on_language_change(self, _value):
        """Handle language selection change"""
        self.update_day_button_colors()
        self.update_quest_display()

    # Timer methods
    def toggle_timer(self):
        """Start or stop the timer"""
        if self.timer_running:
            self.stop_timer()
        else:
            self.start_timer()

    def start_timer(self):
        """Start the timer with daily limit check"""
        today = time.strftime("%Y-%m-%d")
        daily_time = self.daily_time_spent.get(today, 0)

        if daily_time >= 3600:  # 1 hour limit
            self.show_daily_limit_warning()
            return

        self.timer_running = True
        self.start_timer_btn.configure(text="⏸")
        self.timer_thread = threading.Thread(target=self.timer_worker, daemon=True)
        self.timer_thread.start()

    def stop_timer(self):
        """Stop the timer and save daily time"""
        if self.timer_running:
            today = time.strftime("%Y-%m-%d")
            if today not in self.daily_time_spent:
                self.daily_time_spent[today] = 0
            self.daily_time_spent[today] += self.timer_seconds
            self.save_daily_time()

        self.timer_running = False
        self.start_timer_btn.configure(text="▶")

    def reset_timer(self):
        """Reset the timer"""
        self.timer_running = False
        self.timer_seconds = 0
        self.start_timer_btn.configure(text="▶")
        self.update_timer_display()

    def timer_worker(self):
        """Timer background worker"""
        while self.timer_running:
            time.sleep(1)
            if self.timer_running:
                self.timer_seconds += 1
                if self.timer_seconds >= self.timer_duration:
                    self.timer_finished()
                    break

    def timer_finished(self):
        """Handle timer completion"""
        self.stop_timer()
        self.show_feedback("Timer abgelaufen", "⏰ Zeit ist um! Gut gemacht!", "success")

    def show_daily_limit_warning(self):
        """Show warning when daily limit is reached"""
        self.show_feedback(
            "Tageslimit erreicht",
            "⚠️ Du hast heute bereits 1 Stunde gelernt.\nMache eine Pause und komme morgen wieder!",
            "error"
        )

    def on_timer_preset_change(self, preset_name):
        """Handle timer preset change"""
        if preset_name == "Benutzerdefiniert":
            self.open_custom_timer_dialog()
        else:
            self.timer_duration = self.timer_presets[preset_name]
            self.reset_timer()

    def open_custom_timer_dialog(self):
        """Open dialog for custom timer duration"""
        dialog = ctk.CTkInputDialog(
            text="Timer Dauer in Minuten eingeben:",
            title="Benutzerdefinierte Timer Dauer"
        )
        result = dialog.get_input()
        if result:
            try:
                minutes = int(result)
                if minutes > 0:
                    self.timer_duration = minutes * 60
                    self.reset_timer()
                else:
                    self.show_feedback("Fehler", "Dauer muss positiv sein!", "error")
            except ValueError:
                self.show_feedback("Fehler", "Ungültige Eingabe! Bitte eine Zahl eingeben.", "error")

    def update_timer_display(self):
        """Update timer, clock, and daily progress display (matches image format)"""
        # Update timer with progress (matches image: "Timer 06:25 / 60min (11%)")
        minutes = self.timer_seconds // 60
        seconds = self.timer_seconds % 60
        total_minutes = self.timer_duration // 60
        progress_percent = (self.timer_seconds / self.timer_duration * 100) if self.timer_duration > 0 else 0

        timer_text = f"Timer {minutes:02d}:{seconds:02d} / {total_minutes}min ({progress_percent:.0f}%)"
        self.timer_label.configure(text=timer_text)

        # Update clock (matches image: "Uhr 23:39")
        current_time = time.strftime("%H:%M")
        self.clock_label.configure(text=f"Uhr {current_time}")

        # Update daily progress (matches image: "Heute: 6:50 min")
        today = time.strftime("%Y-%m-%d")
        daily_seconds = self.daily_time_spent.get(today, 0) + self.timer_seconds
        daily_minutes = daily_seconds // 60
        daily_seconds_remainder = daily_seconds % 60
        daily_progress_text = f"Heute: {daily_minutes}:{daily_seconds_remainder:02d} min"

        if hasattr(self, 'daily_progress_label'):
            self.daily_progress_label.configure(text=daily_progress_text)

        # Schedule next update
        self.root.after(1000, self.update_timer_display)

    # Settings and additional dialogs
    def open_settings(self):
        """Open settings dialog with progress tracking"""
        settings_window = ctk.CTkToplevel(self.root)
        settings_window.title("Einstellungen & Fortschritt")
        settings_window.geometry("600x500")
        settings_window.transient(self.root)

        # Create tabview
        tabview = ctk.CTkTabview(settings_window)
        tabview.pack(fill="both", expand=True, padx=20, pady=20)

        # Settings tab
        settings_tab = tabview.add("Einstellungen")

        ctk.CTkLabel(
            settings_tab,
            text="⚙️ Einstellungen",
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(pady=20)

        # Theme selection
        theme_frame = ctk.CTkFrame(settings_tab)
        theme_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(theme_frame, text="Theme:").pack(pady=10)

        theme_var = ctk.StringVar(value="dark")
        theme_menu = ctk.CTkOptionMenu(
            theme_frame,
            variable=theme_var,
            values=["dark", "light", "system"],
            command=lambda x: ctk.set_appearance_mode(x)
        )
        theme_menu.pack(pady=10)

        # Progress tab
        progress_tab = tabview.add("Fortschritt")
        self.setup_progress_tab(progress_tab)

    def setup_progress_tab(self, parent):
        """Setup progress tracking tab"""
        ctk.CTkLabel(
            parent,
            text="📊 Dein Fortschritt",
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(pady=20)

        # Current language progress
        language = self.selected_language.get()
        completed_days = len(self.completed_quests.get(language, []))

        progress_frame = ctk.CTkFrame(parent)
        progress_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(
            progress_frame,
            text=f"Abgeschlossene Tage ({language}): {completed_days}/60",
            font=ctk.CTkFont(size=16)
        ).pack(pady=10)

        # Progress bar
        progress_bar = ctk.CTkProgressBar(progress_frame)
        progress_bar.pack(fill="x", padx=20, pady=10)
        progress_bar.set(completed_days / 60)

        # Challenge statistics
        stats_frame = ctk.CTkFrame(parent)
        stats_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(
            stats_frame,
            text="🎯 Challenge Statistiken:",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))

        # Calculate stats
        attempts_data = self.challenge_attempts.get(language, {})
        submissions_data = self.code_submissions.get(language, {})

        total_attempts = sum(attempts_data.values())
        total_submissions = len(submissions_data)

        stats_text = f"Gesamte Versuche: {total_attempts}\n"
        stats_text += f"Eingereichte Lösungen: {total_submissions}\n"
        stats_text += f"Durchschnittliche Versuche: {total_attempts/max(total_submissions, 1):.1f}"

        ctk.CTkLabel(
            stats_frame,
            text=stats_text,
            font=ctk.CTkFont(size=12)
        ).pack(pady=10)

    def open_editor(self):
        """Open external code editor window"""
        editor_window = ctk.CTkToplevel(self.root)
        editor_window.title("Code Editor")
        editor_window.geometry("800x600")
        editor_window.transient(self.root)

        ctk.CTkLabel(
            editor_window,
            text="📝 Code Editor",
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(pady=10)

        # Text editor
        editor_text = ctk.CTkTextbox(
            editor_window,
            font=ctk.CTkFont(family="Courier", size=12)
        )
        editor_text.pack(fill="both", expand=True, padx=20, pady=20)

        # Load current challenge code if available
        if hasattr(self, 'code_editor'):
            current_code = self.code_editor.get("1.0", "end-1c")
            editor_text.insert("1.0", current_code)

    def open_youtube_links(self):
        """Open YouTube links for current quest"""
        language = self.selected_language.get()
        day = self.selected_day.get()

        if not self.is_day_accessible(day):
            self.show_feedback("Fehler", "Dieser Tag ist noch nicht freigeschaltet!", "error")
            return

        quest = self.quest_data[language][day]

        if "youtube_links" in quest:
            self.show_youtube_selection_window(quest["youtube_links"])
        else:
            search_query = f"{language} programming tutorial"
            webbrowser.open(f"https://www.youtube.com/results?search_query={search_query}")

    def show_youtube_selection_window(self, youtube_links):
        """Show window for selecting YouTube videos"""
        youtube_window = ctk.CTkToplevel(self.root)
        youtube_window.title("Lernvideos auswählen")
        youtube_window.geometry("500x400")
        youtube_window.transient(self.root)

        ctk.CTkLabel(
            youtube_window,
            text="📺 Verfügbare Lernvideos",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=20)

        scroll_frame = ctk.CTkScrollableFrame(youtube_window)
        scroll_frame.pack(fill="both", expand=True, padx=20, pady=10)

        for link in youtube_links:
            link_frame = ctk.CTkFrame(scroll_frame)
            link_frame.pack(fill="x", pady=5)

            flag = "🇩🇪" if link.get("language") == "de" else "🇺🇸"
            title_text = f"{flag} {link['title']}"

            title_label = ctk.CTkLabel(
                link_frame,
                text=title_text,
                font=ctk.CTkFont(size=14, weight="bold")
            )
            title_label.pack(pady=5)

            duration_label = ctk.CTkLabel(
                link_frame,
                text=f"Dauer: {link.get('duration', 'N/A')}",
                font=ctk.CTkFont(size=12)
            )
            duration_label.pack()

            open_btn = ctk.CTkButton(
                link_frame,
                text="Video öffnen",
                command=lambda url=link['url']: webbrowser.open(url),
                width=120
            )
            open_btn.pack(pady=5)

        ctk.CTkButton(
            youtube_window,
            text="Schließen",
            command=youtube_window.destroy
        ).pack(pady=10)

    def run(self):
        """Start the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = CodeTrainingApp()
    app.run()
