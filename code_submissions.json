{"Python": {"1": {"code": "# Dein Code hier\n# Schritt 1: <PERSON><PERSON><PERSON>\nname = \"Dein Name\"\nalter = 0  # <PERSON><PERSON>tadt = \"De<PERSON>t\"\n\n# Schritt 2: <PERSON><PERSON><PERSON> eine Liste mit Hobbies\nhobbies = []  # Füge 3 Hobbies hinzu\n\n# Schritt 3: <PERSON><PERSON>chne Geburtsjahr\ngeburtsjahr = 0  # Be<PERSON>chnung hier\n\n# Schritt 4: Gib Informationen aus\nprint(\"Meine Informationen:\")\n# Deine print-Anweisungen hier", "timestamp": 1757454612.1720748, "attempt": 2}, "2": {"code": "# Dein Code hier\nzahlen = [12, -5, 8, 0, -3, 15, 7, -10, 4, 9]\n\n# <PERSON><PERSON><PERSON> für verschiedene Kategorien\ngerade_count = 0\nungerade_count = 0\npositive_count = 0\nnegative_count = 0\n\n# Schleife durch alle Zahlen\nfor zahl in zahlen:\n    # Deine Klassifizierung hier\n    pass\n\n# Ausgabe der Ergebnisse\nprint(\"Ergebnisse:\")\n# Deine print-Anweisungen hier", "timestamp": 1757454651.586484, "attempt": 1}}, "JavaScript": {}, "Java": {}, "C++": {}, "C#": {}, "Go": {}, "Rust": {}, "TypeScript": {}, "PHP": {}, "Ruby": {}, "Swift": {}, "Kotlin": {}}